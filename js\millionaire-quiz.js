// Millionaire Quiz Game Logic
class MillionaireQuiz {
    constructor() {
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.timer = null;
        this.timeLeft = 30;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };
        this.lifelinesUsed = 0;
        
        // Prize amounts
        this.prizeAmounts = [
            100, 200, 300, 500, 1000,      // Questions 1-5
            2000, 4000, 8000, 16000, 32000, // Questions 6-10
            64000, 125000, 250000, 500000, 1000000 // Questions 11-15
        ];
        
        // Safe points (guaranteed money)
        this.safePoints = [4, 9]; // Questions 5 and 10 (0-indexed)
        
        this.initializeElements();
        this.initializeEventListeners();
        this.loadQuestions();
    }
    
    initializeElements() {
        // Modals
        this.instructionModal = document.getElementById('instruction-modal');
        this.gameArea = document.getElementById('game-area');
        this.resultsModal = document.getElementById('results-modal');
        this.audienceModal = document.getElementById('audience-modal');
        this.friendModal = document.getElementById('friend-modal');
        
        // Game elements
        this.questionText = document.getElementById('question-text');
        this.questionNum = document.getElementById('question-num');
        this.questionValue = document.getElementById('question-value');
        this.currentPrize = document.getElementById('current-prize');
        this.timeElement = document.getElementById('time');
        this.optionsContainer = document.getElementById('options-container');
        
        // Buttons
        this.startBtn = document.getElementById('start-btn');
        this.finalAnswerBtn = document.getElementById('final-answer-btn');
        this.walkAwayBtn = document.getElementById('walk-away-btn');
        this.playAgainBtn = document.getElementById('play-again-btn');
        this.backToMenuBtn = document.getElementById('back-to-menu-btn');
        
        // Lifelines
        this.fiftyFiftyBtn = document.getElementById('fifty-fifty');
        this.askAudienceBtn = document.getElementById('ask-audience');
        this.phoneFriendBtn = document.getElementById('phone-friend');

        // Host elements
        this.hostElement = document.getElementById('quiz-host');
        this.hostText = document.querySelector('.host-text');
        this.pikachuCharacter = document.querySelector('.pikachu-character');
        
        // Results
        this.resultTitle = document.getElementById('result-title');
        this.resultMessage = document.getElementById('result-message');
        this.questionsAnswered = document.getElementById('questions-answered');
        this.lifelinesUsedSpan = document.getElementById('lifelines-used');
    }
    
    initializeEventListeners() {
        this.startBtn.addEventListener('click', () => this.startGame());
        this.finalAnswerBtn.addEventListener('click', () => this.submitFinalAnswer());
        this.walkAwayBtn.addEventListener('click', () => this.walkAway());
        this.playAgainBtn.addEventListener('click', () => this.resetGame());
        this.backToMenuBtn.addEventListener('click', () => this.backToMenu());
        
        // Lifelines
        this.fiftyFiftyBtn.addEventListener('click', () => this.useFiftyFifty());
        this.askAudienceBtn.addEventListener('click', () => this.useAskAudience());
        this.phoneFriendBtn.addEventListener('click', () => this.usePhoneFriend());
        
        // Modal close buttons
        document.getElementById('close-audience-modal').addEventListener('click', () => {
            this.audienceModal.classList.remove('show');
        });
        document.getElementById('close-friend-modal').addEventListener('click', () => {
            this.friendModal.classList.remove('show');
        });
        
        // Option selection
        this.optionsContainer.addEventListener('click', (e) => {
            if (e.target.closest('.option') && !e.target.closest('.option').classList.contains('eliminated')) {
                this.selectOption(e.target.closest('.option'));
            }
        });
    }
    
    loadQuestions() {
        // Questions arranged by difficulty (easy to hard)
        this.questions = [
            // Questions 1-5: Easy ($100-$1,000)
            {
                question: "What is the capital of France?",
                options: ["London", "Berlin", "Paris", "Madrid"],
                correct: 2
            },
            {
                question: "Which planet is known as the Red Planet?",
                options: ["Venus", "Mars", "Jupiter", "Saturn"],
                correct: 1
            },
            {
                question: "What is 2 + 2?",
                options: ["3", "4", "5", "6"],
                correct: 1
            },
            {
                question: "Which animal is known as the 'King of the Jungle'?",
                options: ["Tiger", "Elephant", "Lion", "Leopard"],
                correct: 2
            },
            {
                question: "How many days are there in a week?",
                options: ["5", "6", "7", "8"],
                correct: 2
            },

            // Questions 6-10: Medium ($2,000-$32,000)
            {
                question: "Who painted the Mona Lisa?",
                options: ["Vincent van Gogh", "Pablo Picasso", "Leonardo da Vinci", "Michelangelo"],
                correct: 2
            },
            {
                question: "What is the chemical symbol for gold?",
                options: ["Go", "Gd", "Au", "Ag"],
                correct: 2
            },
            {
                question: "Which country has the most natural lakes?",
                options: ["Russia", "Canada", "Finland", "Sweden"],
                correct: 1
            },
            {
                question: "In which year did the Titanic sink?",
                options: ["1910", "1912", "1914", "1916"],
                correct: 1
            },
            {
                question: "What is the smallest prime number?",
                options: ["0", "1", "2", "3"],
                correct: 2
            },

            // Questions 11-15: Hard ($64,000-$1,000,000)
            {
                question: "Which programming language was created by Guido van Rossum?",
                options: ["Java", "Python", "C++", "JavaScript"],
                correct: 1
            },
            {
                question: "What is the speed of light in vacuum (approximately)?",
                options: ["299,792,458 m/s", "300,000,000 m/s", "299,000,000 m/s", "298,792,458 m/s"],
                correct: 0
            },
            {
                question: "Which element has the highest melting point?",
                options: ["Carbon", "Tungsten", "Rhenium", "Osmium"],
                correct: 1
            },
            {
                question: "In quantum mechanics, what principle states that you cannot simultaneously know both position and momentum of a particle?",
                options: ["Pauli Exclusion Principle", "Heisenberg Uncertainty Principle", "Schrödinger's Principle", "Planck's Principle"],
                correct: 1
            },
            {
                question: "What is the name of the theoretical boundary around a black hole beyond which nothing can escape?",
                options: ["Photon Sphere", "Ergosphere", "Event Horizon", "Singularity"],
                correct: 2
            }
        ];
    }
    
    startGame() {
        this.instructionModal.classList.remove('show');
        this.gameArea.style.display = 'block';
        this.gameActive = true;
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.lifelinesUsed = 0;

        // Reset lifelines
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };

        this.updateLifelineButtons();
        this.displayQuestion();
        this.updatePrizeLadder();
        this.startTimer();

        // Special entrance animation for Pikachu
        this.hostEntrance();
    }
    
    displayQuestion() {
        if (this.currentQuestion >= this.questions.length) {
            this.endGame(true, this.prizeAmounts[this.currentQuestion - 1]);
            return;
        }

        const question = this.questions[this.currentQuestion];
        this.questionText.textContent = question.question;
        this.questionNum.textContent = this.currentQuestion + 1;
        this.questionValue.textContent = this.prizeAmounts[this.currentQuestion].toLocaleString();



        // Update options
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach((option, index) => {
            const optionText = option.querySelector('.option-text');
            optionText.textContent = question.options[index];
            option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
            // Ensure pointer events are enabled
            option.style.pointerEvents = 'auto';
            // Clear any inline opacity styles
            option.style.opacity = '';
        });

        this.selectedAnswer = null;
        this.finalAnswerBtn.disabled = true;
        this.updatePrizeLadder();

        // Update host interaction
        this.updateHostMessage();
    }
    
    selectOption(optionElement) {
        // Remove previous selection
        this.optionsContainer.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        // Select new option
        optionElement.classList.add('selected');
        this.selectedAnswer = Array.from(this.optionsContainer.querySelectorAll('.option')).indexOf(optionElement);
        this.finalAnswerBtn.disabled = false;
    }
    
    startTimer() {
        this.timeLeft = 30;
        this.timeElement.textContent = this.timeLeft;
        
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.timeElement.textContent = this.timeLeft;
            
            if (this.timeLeft <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        if (this.selectedAnswer === null) {
            this.endGame(false, this.getGuaranteedMoney());
        } else {
            this.submitFinalAnswer();
        }
    }
    
    submitFinalAnswer() {
        if (this.selectedAnswer === null) return;

        // Disable the Final Answer button immediately to prevent multiple clicks
        this.finalAnswerBtn.disabled = true;

        // Confirmation dialog
        const selectedOption = this.optionsContainer.querySelectorAll('.option')[this.selectedAnswer];
        const selectedText = selectedOption.querySelector('.option-text').textContent;
        const optionLetter = selectedOption.querySelector('.option-letter').textContent;

        if (!confirm(`Are you sure you want to lock in ${optionLetter} ${selectedText} as your final answer?`)) {
            // Re-enable the button if user cancels
            this.finalAnswerBtn.disabled = false;
            return;
        }

        // Only stop the timer after confirmation
        this.stopTimer();
        this.gameActive = false;

        const question = this.questions[this.currentQuestion];
        const isCorrect = this.selectedAnswer === question.correct;

        // Disable all options during reveal
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach(option => option.style.pointerEvents = 'none');

        // Dramatic pause before revealing answer
        setTimeout(() => {
            // Show correct answer
            options[question.correct].classList.add('correct');

            if (isCorrect) {
                this.playSound('correct');
                this.hostReaction(true);
                // Flash the correct answer
                let flashCount = 0;
                const flashInterval = setInterval(() => {
                    options[question.correct].style.opacity = flashCount % 2 === 0 ? '0.5' : '1';
                    flashCount++;
                    if (flashCount >= 6) {
                        clearInterval(flashInterval);
                        options[question.correct].style.opacity = '1';
                    }
                }, 200);
            } else {
                options[this.selectedAnswer].classList.add('incorrect');
                this.playSound('wrong');
                this.hostReaction(false);
            }

            setTimeout(() => {
                if (isCorrect) {
                    this.currentQuestion++;
                    if (this.currentQuestion >= this.questions.length) {
                        // Won the game!
                        this.playSound('success');
                        this.endGame(true, 1000000);
                    } else {
                        // Check if reached a milestone
                        if (this.safePoints.includes(this.currentQuestion - 1)) {
                            alert(`Congratulations! You've reached a safe point with $${this.prizeAmounts[this.currentQuestion - 1].toLocaleString()}!`);
                        }

                        this.gameActive = true;
                        options.forEach(option => option.style.pointerEvents = 'auto');
                        // Make sure Final Answer button stays disabled until a new option is selected
                        this.finalAnswerBtn.disabled = true;
                        this.displayQuestion();
                        this.startTimer();
                    }
                } else {
                    // Wrong answer - game over
                    this.endGame(false, this.getGuaranteedMoney());
                }
            }, 3000);
        }, 1000);
    }
    
    getGuaranteedMoney() {
        let guaranteed = 0;
        for (let i = this.safePoints.length - 1; i >= 0; i--) {
            if (this.currentQuestion > this.safePoints[i]) {
                guaranteed = this.prizeAmounts[this.safePoints[i]];
                break;
            }
        }
        return guaranteed;
    }
    
    walkAway() {
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.endGame(true, currentWinnings, true);
    }
    
    updatePrizeLadder() {
        // Update horizontal prize ladder
        const prizeItems = document.querySelectorAll('.prize-item-horizontal');
        prizeItems.forEach((item, index) => {
            const level = parseInt(item.dataset.level);
            item.classList.remove('current', 'completed');

            if (level === this.currentQuestion + 1) {
                item.classList.add('current');
                // Scroll to current item
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            } else if (level <= this.currentQuestion) {
                item.classList.add('completed');
            }
        });

        // Update current prize display
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.currentPrize.textContent = currentWinnings.toLocaleString();
    }

    // Host interaction methods
    updateHostMessage() {
        if (!this.hostText) return;

        // Different messages based on question difficulty and prize level
        let questionMessages;
        const prizeValue = this.prizeAmounts[this.currentQuestion];

        if (this.currentQuestion < 5) {
            questionMessages = [
                "Let's start with an easy one! ⚡",
                "This should be simple! 😊",
                "Warming up time! 🔥",
                "Easy money coming up! 💰",
                "You've got this! 💪"
            ];
        } else if (this.currentQuestion < 10) {
            questionMessages = [
                "Getting tougher now! 🤔",
                "Think it through! 🧠",
                "This one's tricky! ⚡",
                `$${prizeValue.toLocaleString()} question! 💰`,
                "Stay focused! 👀"
            ];
        } else if (this.currentQuestion < 14) {
            questionMessages = [
                "Big money question! 💎",
                "This is getting serious! ⚡",
                "Almost there! 🚀",
                `$${prizeValue.toLocaleString()} on the line! 💰`,
                "You're doing amazing! ⭐"
            ];
        } else {
            questionMessages = [
                "MILLIONAIRE QUESTION! 👑",
                "This is it! The big one! 💎",
                "One million dollars! 🤑",
                "Make history! ⚡",
                "Pika believes in you! 💪"
            ];
        }

        const randomMessage = questionMessages[Math.floor(Math.random() * questionMessages.length)];
        this.hostText.textContent = randomMessage;

        // Add special styling for high-value questions
        if (this.currentQuestion >= 10) {
            this.hostText.style.color = '#ffd700';
            this.hostText.style.fontWeight = 'bold';
        } else {
            this.hostText.style.color = '#2c3e50';
            this.hostText.style.fontWeight = '600';
        }
    }

    hostReaction(isCorrect) {
        if (!this.pikachuCharacter) return;

        if (isCorrect) {
            // Happy reaction with enhanced animation
            this.pikachuCharacter.style.animation = 'none';
            this.pikachuCharacter.offsetHeight; // Trigger reflow
            this.pikachuCharacter.style.animation = 'celebration 0.8s ease-in-out 4';

            if (this.hostText) {
                let happyMessages;
                const prizeValue = this.prizeAmounts[this.currentQuestion - 1];

                if (this.currentQuestion <= 5) {
                    happyMessages = [
                        "Excellent! ⚡",
                        "Well done! 😊",
                        "Correct! 🎉",
                        "Perfect! ⭐",
                        "Pika pika! 💛"
                    ];
                } else if (this.currentQuestion <= 10) {
                    happyMessages = [
                        "Amazing work! 🔥",
                        "You're on fire! ⚡",
                        "Brilliant! 🌟",
                        `$${prizeValue.toLocaleString()} earned! 💰`,
                        "Keep it up! 🚀"
                    ];
                } else if (this.currentQuestion <= 14) {
                    happyMessages = [
                        "INCREDIBLE! 💎",
                        "You're unstoppable! ⚡",
                        "AMAZING! 🏆",
                        `$${prizeValue.toLocaleString()} WON! 💰`,
                        "Almost a millionaire! 👑"
                    ];
                } else {
                    happyMessages = [
                        "MILLIONAIRE! 👑💰",
                        "YOU DID IT! 🎉💎",
                        "PIKA PIKA CHAMPION! ⚡👑",
                        "LEGENDARY! 🏆⭐",
                        "ONE MILLION DOLLARS! 💰🎊"
                    ];
                }

                this.hostText.textContent = happyMessages[Math.floor(Math.random() * happyMessages.length)];
                this.hostText.style.color = '#ffd700';
                this.hostText.style.fontWeight = 'bold';
            }
        } else {
            // Sad reaction
            if (this.hostText) {
                const sadMessages = [
                    "Oh no... 😢",
                    "So close! 💔",
                    "Don't give up! 💪",
                    "You tried your best! ⭐",
                    "Pika... 😔",
                    "Better luck next time! 🍀"
                ];
                this.hostText.textContent = sadMessages[Math.floor(Math.random() * sadMessages.length)];
                this.hostText.style.color = '#e74c3c';
                this.hostText.style.fontWeight = '600';
            }
        }

        // Reset animation after reaction
        setTimeout(() => {
            if (this.pikachuCharacter) {
                this.pikachuCharacter.style.animation = 'hostBounce 4s ease-in-out infinite';
            }
        }, 3000);
    }

    hostEntrance() {
        if (!this.hostElement) return;

        // Start with Pikachu hidden and slide in
        this.hostElement.style.transform = 'translateX(-100px)';
        this.hostElement.style.opacity = '0';

        setTimeout(() => {
            this.hostElement.style.transition = 'all 1s ease-out';
            this.hostElement.style.transform = 'translateX(0)';
            this.hostElement.style.opacity = '1';

            // Welcome message
            if (this.hostText) {
                this.hostText.textContent = "Hi! I'm Pikachu! ⚡";
                this.hostText.style.color = '#ffd700';

                setTimeout(() => {
                    this.updateHostMessage();
                }, 2000);
            }
        }, 500);
    }



    // Lifeline Methods
    useFiftyFifty() {
        if (!this.lifelines.fiftyFifty || !this.gameActive) return;

        this.lifelines.fiftyFifty = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();


        const question = this.questions[this.currentQuestion];
        const options = this.optionsContainer.querySelectorAll('.option');
        const incorrectOptions = [];

        // Find incorrect options
        options.forEach((option, index) => {
            if (index !== question.correct) {
                incorrectOptions.push(index);
            }
        });

        // Randomly eliminate 2 incorrect options
        const toEliminate = this.shuffleArray(incorrectOptions).slice(0, 2);
        toEliminate.forEach(index => {
            options[index].classList.add('eliminated');
        });

        this.playSound('correct'); // Use existing sound
    }

    useAskAudience() {
        if (!this.lifelines.askAudience || !this.gameActive) return;

        this.lifelines.askAudience = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();


        const question = this.questions[this.currentQuestion];
        const audienceBars = this.audienceModal.querySelectorAll('.audience-bar');

        // Generate audience percentages (biased toward correct answer)
        const percentages = this.generateAudiencePercentages(question.correct);

        audienceBars.forEach((bar, index) => {
            const barElement = bar.querySelector('.bar');
            const percentageElement = bar.querySelector('.percentage');

            setTimeout(() => {
                barElement.style.width = percentages[index] + '%';
                percentageElement.textContent = percentages[index] + '%';
            }, index * 200);
        });

        this.audienceModal.classList.add('show');
        this.playSound('correct');
    }

    usePhoneFriend() {
        if (!this.lifelines.phoneFreind || !this.gameActive) return;

        this.lifelines.phoneFreind = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();


        const question = this.questions[this.currentQuestion];
        const friendMessage = document.getElementById('friend-message');

        // Generate friend's advice (usually helpful but not always certain)
        const advice = this.generateFriendAdvice(question);

        friendMessage.textContent = "Your friend is thinking...";
        this.friendModal.classList.add('show');

        setTimeout(() => {
            friendMessage.textContent = advice;
        }, 2000);

        this.playSound('correct');
    }

    generateAudiencePercentages(correctIndex) {
        const percentages = [0, 0, 0, 0];

        // Give correct answer 40-70% of votes
        const correctPercentage = 40 + Math.random() * 30;
        percentages[correctIndex] = Math.round(correctPercentage);

        // Distribute remaining percentage among other options
        let remaining = 100 - percentages[correctIndex];
        for (let i = 0; i < 4; i++) {
            if (i !== correctIndex) {
                if (remaining > 0) {
                    const percentage = Math.random() * remaining;
                    percentages[i] = Math.round(percentage);
                    remaining -= percentages[i];
                }
            }
        }

        // Adjust for rounding errors
        const total = percentages.reduce((sum, p) => sum + p, 0);
        if (total !== 100) {
            percentages[correctIndex] += (100 - total);
        }

        return percentages;
    }

    generateFriendAdvice(question) {
        const options = ['A', 'B', 'C', 'D'];
        const correctOption = options[question.correct];

        const adviceTemplates = [
            `I'm pretty sure it's ${correctOption}. That sounds right to me.`,
            `Hmm, I think it might be ${correctOption}, but I'm not 100% certain.`,
            `I remember learning about this - I believe it's ${correctOption}.`,
            `My gut feeling says ${correctOption}. Hope that helps!`,
            `I'd go with ${correctOption} if I were you.`
        ];

        // 80% chance of giving correct advice
        if (Math.random() < 0.8) {
            return adviceTemplates[Math.floor(Math.random() * adviceTemplates.length)];
        } else {
            // 20% chance of being uncertain or giving wrong advice
            const wrongOption = options[Math.floor(Math.random() * 4)];
            return `I'm not really sure about this one. Maybe ${wrongOption}? Sorry, I wish I could be more help.`;
        }
    }

    updateLifelineButtons() {
        this.fiftyFiftyBtn.disabled = !this.lifelines.fiftyFifty;
        this.askAudienceBtn.disabled = !this.lifelines.askAudience;
        this.phoneFriendBtn.disabled = !this.lifelines.phoneFreind;
    }

    endGame(won, winnings, walkedAway = false) {
        this.stopTimer();
        this.gameActive = false;

        // Update result display
        if (walkedAway) {
            this.resultTitle.textContent = "You Walked Away!";
            this.resultMessage.textContent = `You walked away with $${winnings.toLocaleString()}!`;
        } else if (won && winnings === 1000000) {
            this.resultTitle.textContent = "🎉 MILLIONAIRE! 🎉";
            this.resultMessage.textContent = "Congratulations! You won $1,000,000!";

            // Special host celebration for millionaire win
            if (this.hostText) {
                this.hostText.textContent = "PIKA PIKA! You did it!";
            }
            if (this.pikachuCharacter) {
                this.pikachuCharacter.style.animation = 'celebration 1s ease-in-out infinite';
            }

            // Add celebration effects for millionaire win
            setTimeout(() => {
                this.resultsModal.querySelector('.modal-content').classList.add('millionaire-celebration');
                this.createConfetti();
            }, 1500);
        } else if (won) {
            this.resultTitle.textContent = "Congratulations!";
            this.resultMessage.textContent = `You won $${winnings.toLocaleString()}!`;
        } else {
            this.resultTitle.textContent = "Game Over!";
            this.resultMessage.textContent = `You leave with $${winnings.toLocaleString()}.`;
        }

        this.questionsAnswered.textContent = this.currentQuestion;
        this.lifelinesUsedSpan.textContent = this.lifelinesUsed;

        setTimeout(() => {
            this.resultsModal.classList.add('show');
        }, 1000);
    }

    createConfetti() {
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.animationDelay = Math.random() * 3 + 's';
                document.body.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 3000);
            }, i * 100);
        }
    }

    resetGame() {
        this.resultsModal.classList.remove('show');
        this.gameArea.style.display = 'none';
        this.instructionModal.classList.add('show');

        // Reset all game state
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.lifelinesUsed = 0;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };

        // Reset option elements completely
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach(option => {
            // Remove all game-related CSS classes
            option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
            // Restore pointer events
            option.style.pointerEvents = 'auto';
            // Clear any inline opacity styles
            option.style.opacity = '';
        });

        // Reset final answer button
        this.finalAnswerBtn.disabled = true;

        // Reset lifeline buttons
        this.updateLifelineButtons();

        // Reset prize ladder
        const prizeItems = document.querySelectorAll('.prize-item-horizontal');
        prizeItems.forEach(item => {
            item.classList.remove('current', 'completed');
        });

        // Reset current prize display
        this.currentPrize.textContent = '0';

        // Reset timer display
        this.timeElement.textContent = '30';

        // Reset host state
        if (this.hostElement) {
            this.hostElement.style.transform = '';
            this.hostElement.style.opacity = '';
            this.hostElement.style.transition = '';
        }

        // Reset Pikachu animation
        if (this.pikachuCharacter) {
            this.pikachuCharacter.style.animation = 'hostBounce 4s ease-in-out infinite';
        }

        // Reset host text
        if (this.hostText) {
            this.hostText.textContent = "Here's your question!";
            this.hostText.style.color = '#2c3e50';
            this.hostText.style.fontWeight = '600';
        }

        this.stopTimer();
    }

    backToMenu() {
        window.location.href = 'mainpage.html';
    }

    // Utility methods
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    playSound(type) {
        // Use existing sound files if available
        try {
            let audio;
            switch(type) {
                case 'correct':
                    audio = new Audio('/sounds/correct.mp3');
                    break;
                case 'wrong':
                    audio = new Audio('/sounds/wrong.mp3');
                    break;
                case 'success':
                    audio = new Audio('/sounds/success.mp3');
                    break;
                default:
                    return;
            }
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Audio play failed:', e));
        } catch (e) {
            console.log('Audio not available:', e);
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new MillionaireQuiz();
    // Show the instruction modal initially
    game.instructionModal.classList.add('show');
});
